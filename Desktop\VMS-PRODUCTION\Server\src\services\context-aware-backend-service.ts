/**
 * CONTEXT-AWARE BACKEND SERVICE
 * =============================
 * 
 * Backend service that integrates with the frontend context-aware system
 * Provides server-side validation and enforcement of workflow rules
 */

import { logger } from '../utils/logger.js';
import { query } from '../database/db.js';

export interface BackendVoucherOperation {
  type: 'CREATE' | 'UPDATE' | 'DELETE' | 'DISPATCH' | 'REJECT' | 'RESUBMIT' | 'RETURN' | 'CERTIFY';
  voucherId: string;
  newState?: string;
  metadata?: Record<string, any>;
  user?: any;
}

export interface BackendAuthorizationResult {
  authorized: boolean;
  violations: Array<{
    voucherId: string;
    violationType: string;
    description: string;
    expectedState: string;
    actualState: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }>;
  warnings: string[];
  blockingReason?: string;
  recommendedAction?: string;
}

/**
 * CONTEXT-AWARE BACKEND SERVICE
 * Provides server-side workflow validation and enforcement
 */
export class ContextAwareBackendService {
  
  /**
   * VALIDATE VOUCHER OPERATION
   * Server-side validation of voucher operations
   */
  static async validateOperation(
    operation: BackendVoucherOperation,
    voucher: any,
    user: any
  ): Promise<BackendAuthorizationResult> {
    try {
      logger.info(`🛡️ Backend Context-Aware Validation: ${operation.type} for voucher ${voucher.id}`);
      
      const violations: BackendAuthorizationResult['violations'] = [];
      const warnings: string[] = [];
      
      // CRITICAL RULE 1: Cannot dispatch rejected copies
      if (operation.type === 'DISPATCH' && voucher.voucher_type === 'COPY' && voucher.is_rejected) {
        violations.push({
          voucherId: voucher.id,
          violationType: 'INVALID_TAB_PLACEMENT',
          description: 'Cannot dispatch rejected copy voucher - copies are permanent records only',
          expectedState: 'Permanent record in REJECTED tab',
          actualState: 'Attempting dispatch operation',
          severity: 'CRITICAL'
        });
      }
      
      // CRITICAL RULE 2: Cannot certify rejected vouchers directly
      if (operation.type === 'CERTIFY' && voucher.is_rejected) {
        violations.push({
          voucherId: voucher.id,
          violationType: 'WORKFLOW_BYPASS',
          description: 'Cannot certify rejected voucher directly - must be resubmitted first',
          expectedState: 'Resubmitted voucher in normal workflow',
          actualState: 'Rejected voucher attempting direct certification',
          severity: 'CRITICAL'
        });
      }
      
      // CRITICAL RULE 3: Cannot reject already rejected vouchers
      if (operation.type === 'REJECT' && voucher.is_rejected) {
        violations.push({
          voucherId: voucher.id,
          violationType: 'WORKFLOW_BYPASS',
          description: 'Cannot reject already rejected voucher',
          expectedState: 'Normal or resubmitted voucher',
          actualState: 'Already rejected voucher',
          severity: 'HIGH'
        });
      }
      
      // CRITICAL RULE 4: Validate department dispatch permissions
      if (operation.type === 'DISPATCH') {
        // Finance can dispatch vouchers TO Audit (when voucher is in FINANCE)
        // Audit can dispatch vouchers BACK TO Finance (when voucher is in AUDIT)
        const isFinanceDispatchingToAudit = (user.department === 'FINANCE' && voucher.department === 'FINANCE');
        const isAuditDispatchingToFinance = (user.department === 'AUDIT' && voucher.department === 'AUDIT');

        if (!isFinanceDispatchingToAudit && !isAuditDispatchingToFinance) {
          violations.push({
            voucherId: voucher.id,
            violationType: 'DEPARTMENT_VIOLATION',
            description: `${user.department} user cannot dispatch voucher from ${voucher.department} department`,
            expectedState: `User department matches voucher department for dispatch`,
            actualState: `${user.department} user trying to dispatch ${voucher.department} voucher`,
            severity: 'HIGH'
          });
        }
      }
      
      // CRITICAL RULE 5: REJECTED VOUCHERS CANNOT GET CERTIFIED STATUS
      if (operation.type === 'DISPATCH' && voucher.rejected_by && operation.newState === 'VOUCHER CERTIFIED') {
        violations.push({
          voucherId: voucher.id,
          violationType: 'REJECTED_VOUCHER_CERTIFIED_STATUS',
          description: `CRITICAL VIOLATION: Rejected voucher (rejected by ${voucher.rejected_by}) cannot be given CERTIFIED status during dispatch`,
          expectedState: 'VOUCHER REJECTED status for rejected vouchers',
          actualState: 'VOUCHER CERTIFIED status for rejected voucher',
          severity: 'CRITICAL'
        });
      }

      // CRITICAL RULE 6: Validate user permissions
      if (operation.type === 'CERTIFY' && user.department !== 'AUDIT') {
        violations.push({
          voucherId: voucher.id,
          violationType: 'DEPARTMENT_VIOLATION',
          description: 'Only AUDIT department can certify vouchers',
          expectedState: 'AUDIT user',
          actualState: `${user.department} user`,
          severity: 'HIGH'
        });
      }
      
      // WARNING: Check for unusual patterns
      if (operation.type === 'UPDATE' && voucher.resubmissionCount > 3) {
        warnings.push(`Voucher ${voucher.id} has been resubmitted ${voucher.resubmissionCount} times`);
      }
      
      const authorized = violations.length === 0;
      
      if (authorized) {
        logger.info(`✅ Backend Context-Aware: Operation ${operation.type} AUTHORIZED for voucher ${voucher.id}`);
      } else {
        logger.warn(`❌ Backend Context-Aware: Operation ${operation.type} BLOCKED for voucher ${voucher.id}`, {
          violations: violations.map(v => v.description)
        });
      }
      
      return {
        authorized,
        violations,
        warnings,
        blockingReason: authorized ? undefined : violations[0]?.description,
        recommendedAction: authorized ? undefined : this.getRecommendedAction(operation, voucher)
      };
      
    } catch (error) {
      logger.error('Backend Context-Aware validation error:', error);
      
      return {
        authorized: false,
        violations: [{
          voucherId: voucher.id,
          violationType: 'WORKFLOW_BYPASS',
          description: 'Backend validation system error',
          expectedState: 'Valid operation',
          actualState: 'System error',
          severity: 'CRITICAL'
        }],
        warnings: [],
        blockingReason: 'Backend validation system error'
      };
    }
  }
  
  /**
   * VALIDATE BATCH OPERATION
   * Server-side validation of batch operations
   */
  static async validateBatchOperation(
    operation: BackendVoucherOperation,
    voucherIds: string[],
    user: any
  ): Promise<BackendAuthorizationResult> {
    try {
      logger.info(`🛡️ Backend Context-Aware Batch Validation: ${operation.type} for ${voucherIds.length} vouchers`);
      
      const allViolations: BackendAuthorizationResult['violations'] = [];
      const allWarnings: string[] = [];
      
      // Validate each voucher in the batch
      for (const voucherId of voucherIds) {
        const vouchers = await query('SELECT * FROM vouchers WHERE id = ? AND deleted = 0', [voucherId]);
        
        if (vouchers.length === 0) {
          allViolations.push({
            voucherId,
            violationType: 'VOUCHER_NOT_FOUND',
            description: `Voucher ${voucherId} not found`,
            expectedState: 'Existing voucher',
            actualState: 'Not found',
            severity: 'HIGH'
          });
          continue;
        }
        
        const voucher = vouchers[0];
        const validation = await this.validateOperation(operation, voucher, user);
        
        allViolations.push(...validation.violations);
        allWarnings.push(...validation.warnings);
      }
      
      const authorized = allViolations.length === 0;
      
      logger.info(`${authorized ? '✅' : '❌'} Backend Context-Aware Batch: ${operation.type} ${authorized ? 'AUTHORIZED' : 'BLOCKED'} for ${voucherIds.length} vouchers`);
      
      return {
        authorized,
        violations: allViolations,
        warnings: allWarnings,
        blockingReason: authorized ? undefined : `${allViolations.length} violations found in batch`,
        recommendedAction: authorized ? undefined : 'Review individual voucher violations'
      };
      
    } catch (error) {
      logger.error('Backend Context-Aware batch validation error:', error);
      
      return {
        authorized: false,
        violations: [{
          voucherId: 'BATCH',
          violationType: 'WORKFLOW_BYPASS',
          description: 'Backend batch validation system error',
          expectedState: 'Valid batch operation',
          actualState: 'System error',
          severity: 'CRITICAL'
        }],
        warnings: [],
        blockingReason: 'Backend batch validation system error'
      };
    }
  }
  
  /**
   * GET VOUCHER CLASSIFICATION
   * Server-side voucher classification for API responses
   */
  static async getVoucherClassification(voucher: any, department: string): Promise<any> {
    try {
      // Basic server-side classification
      const classification = {
        type: 'NORMAL',
        workflowState: 'PENDING',
        isOriginal: voucher.voucher_type !== 'COPY',
        isCopy: voucher.voucher_type === 'COPY',
        isRejected: voucher.is_rejected === 1 || voucher.is_rejected === true,
        isResubmitted: (voucher.resubmissionCount || 0) > 0,
        isReturned: voucher.status === 'VOUCHER_RETURNED' || voucher.isReturned === true,
        isCertified: voucher.status === 'VOUCHER_CERTIFIED',
        hasBeenDispatched: !!(voucher.auditDispatchedBy && voucher.auditDispatchTime),
        department: voucher.department,
        originalDepartment: voucher.originalDepartment || voucher.original_department
      };
      
      // Determine voucher type
      if (classification.isReturned && classification.isCopy) {
        classification.type = 'RETURNED_COPY';
      } else if (classification.isReturned && classification.isOriginal) {
        classification.type = 'RETURNED_ORIGINAL';
      } else if (classification.isRejected && classification.isCopy) {
        classification.type = 'REJECTED_COPY';
      } else if (classification.isRejected && classification.isOriginal) {
        classification.type = 'REJECTED_ORIGINAL';
      } else if (classification.isResubmitted) {
        classification.type = 'RESUBMITTED';
      }
      
      // Determine workflow state
      if (classification.isCertified) {
        classification.workflowState = 'CERTIFIED';
      } else if (classification.hasBeenDispatched) {
        classification.workflowState = 'DISPATCHED';
      } else if (voucher.workStarted) {
        classification.workflowState = 'PENDING_DISPATCH';
      } else if (voucher.receivedByAudit) {
        classification.workflowState = 'NEW_VOUCHERS';
      } else if (voucher.sentToAudit) {
        classification.workflowState = 'PROCESSING';
      }
      
      return classification;
      
    } catch (error) {
      logger.error('Backend voucher classification error:', error);
      return {
        type: 'NORMAL',
        workflowState: 'PENDING',
        error: 'Classification failed'
      };
    }
  }
  
  /**
   * PRIVATE: Get recommended action for blocked operation
   */
  private static getRecommendedAction(operation: BackendVoucherOperation, voucher: any): string {
    if (operation.type === 'CERTIFY' && voucher.is_rejected) {
      return 'Resubmit the voucher first, then process through normal workflow';
    }
    
    if (operation.type === 'DISPATCH' && voucher.voucher_type === 'COPY') {
      return 'Copy vouchers are permanent records and cannot be dispatched';
    }
    
    if (operation.type === 'REJECT' && voucher.is_rejected) {
      return 'Voucher is already rejected - use resubmission workflow instead';
    }
    
    return 'Follow the validated workflow rules for this voucher type';
  }
  
  /**
   * LOG OPERATION RESULT
   * Log the result of context-aware operations for monitoring
   */
  static logOperationResult(
    operation: BackendVoucherOperation,
    result: BackendAuthorizationResult,
    user: any
  ): void {
    const logData = {
      operation: operation.type,
      voucherId: operation.voucherId,
      authorized: result.authorized,
      violationCount: result.violations.length,
      warningCount: result.warnings.length,
      user: user.name,
      department: user.department,
      timestamp: new Date().toISOString()
    };
    
    if (result.authorized) {
      logger.info('✅ Context-Aware Operation Authorized', logData);
    } else {
      logger.warn('❌ Context-Aware Operation Blocked', {
        ...logData,
        blockingReason: result.blockingReason,
        violations: result.violations.map(v => v.description)
      });
    }
  }
}
