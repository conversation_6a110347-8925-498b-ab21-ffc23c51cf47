const mysql = require('mysql2/promise');

async function analyzeDatabases() {
  console.log('🔍 Analyzing VMS Databases...\n');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'vms@2025@1989'
  });

  const vmsDatabases = ['vms30', 'vms_2026', 'vms_clean_db', 'vms_db', 'vms_production'];

  for (const dbName of vmsDatabases) {
    console.log(`\n📊 === DATABASE: ${dbName} ===`);
    
    try {
      await connection.execute(`USE ${dbName}`);
      
      // Check if key tables exist
      const [tables] = await connection.execute('SHOW TABLES');
      const tableNames = tables.map(t => Object.values(t)[0]);
      
      console.log(`📋 Tables (${tableNames.length}):`, tableNames.slice(0, 10).join(', ') + (tableNames.length > 10 ? '...' : ''));
      
      // Check for key VMS tables
      const keyTables = ['users', 'vouchers', 'batches', 'audit_logs'];
      const hasKeyTables = keyTables.filter(table => tableNames.includes(table));
      console.log(`🔑 Key VMS tables: ${hasKeyTables.join(', ')} (${hasKeyTables.length}/${keyTables.length})`);
      
      if (hasKeyTables.includes('users')) {
        const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
        console.log(`👥 Users: ${userCount[0].count}`);
        
        // Check users table schema
        const [userCols] = await connection.execute('DESCRIBE users');
        const userColumns = userCols.map(col => col.Field);
        const hasLastSelectedYear = userColumns.includes('last_selected_year');
        console.log(`   - last_selected_year column: ${hasLastSelectedYear ? '✅' : '❌'}`);
      }
      
      if (hasKeyTables.includes('vouchers')) {
        const [voucherCount] = await connection.execute('SELECT COUNT(*) as count FROM vouchers');
        console.log(`📄 Vouchers: ${voucherCount[0].count}`);
        
        // Check vouchers table schema
        const [voucherCols] = await connection.execute('DESCRIBE vouchers');
        const voucherColumns = voucherCols.map(col => col.Field);
        const hasIsRejected = voucherColumns.includes('is_rejected');
        const hasRejectionType = voucherColumns.includes('rejection_type');
        console.log(`   - is_rejected column: ${hasIsRejected ? '✅' : '❌'}`);
        console.log(`   - rejection_type column: ${hasRejectionType ? '✅' : '❌'}`);
      }
      
      if (hasKeyTables.includes('audit_logs')) {
        const [auditCount] = await connection.execute('SELECT COUNT(*) as count FROM audit_logs');
        console.log(`📝 Audit logs: ${auditCount[0].count}`);
        
        // Check audit_logs table schema
        const [auditCols] = await connection.execute('DESCRIBE audit_logs');
        const auditColumns = auditCols.map(col => col.Field);
        const hasUserName = auditColumns.includes('user_name');
        console.log(`   - user_name column: ${hasUserName ? '✅' : '❌'}`);
      }
      
      if (hasKeyTables.includes('batches')) {
        const [batchCount] = await connection.execute('SELECT COUNT(*) as count FROM batches');
        console.log(`📦 Batches: ${batchCount[0].count}`);
      }
      
      // Calculate completeness score
      const schemaScore = [
        hasKeyTables.length >= 4,
        hasKeyTables.includes('users') && userColumns?.includes('last_selected_year'),
        hasKeyTables.includes('vouchers') && voucherColumns?.includes('is_rejected'),
        hasKeyTables.includes('audit_logs') && auditColumns?.includes('user_name')
      ].filter(Boolean).length;
      
      console.log(`🎯 Schema completeness: ${schemaScore}/4 ${schemaScore === 4 ? '✅ COMPLETE' : '⚠️ INCOMPLETE'}`);
      
    } catch (error) {
      console.log(`❌ Error accessing ${dbName}:`, error.message);
    }
  }
  
  console.log('\n🎯 RECOMMENDATION:');
  console.log('Based on the analysis above, identify which database has:');
  console.log('1. All key tables (users, vouchers, batches, audit_logs)');
  console.log('2. Complete schema with all required columns');
  console.log('3. Actual data (users > 0, vouchers > 0)');
  console.log('4. Most recent/complete structure');
  
  await connection.end();
}

analyzeDatabases().catch(console.error);
