/**
 * CONTEXT-AWARE VETO MIDDLEWARE
 * =============================
 * 
 * Backend middleware that enforces context-aware system authority
 * All voucher operations must pass through this validation layer
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger.js';

// Import types (will be created)
interface VoucherOperation {
  type: 'CREATE' | 'UPDATE' | 'DELETE' | 'DISPATCH' | 'REJECT' | 'RESUBMIT' | 'RETURN' | 'CERTIFY';
  voucherId: string;
  newState?: string;
  metadata?: Record<string, any>;
}

interface AuthorizationResult {
  authorized: boolean;
  violations: Array<{
    voucherId: string;
    violationType: string;
    description: string;
    expectedState: string;
    actualState: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }>;
  warnings: string[];
  recommendedAction?: string;
  blockingReason?: string;
}

// Extend Request interface to include context authorization
declare global {
  namespace Express {
    interface Request {
      contextAuthorization?: AuthorizationResult;
      voucherOperation?: VoucherOperation;
    }
  }
}

/**
 * CONTEXT-AWARE VETO MIDDLEWARE
 * Supreme authority validation for all voucher operations
 */
export const contextAwareVeto = (operationType: VoucherOperation['type']) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      logger.info(`🛡️ Context-Aware Veto: Validating ${operationType} operation`, {
        operation: operationType,
        voucherId: req.params.id,
        userId: req.user?.id,
        department: req.user?.department
      });

      // Get voucher from database
      const voucher = await getVoucherFromDatabase(req.params.id);
      if (!voucher) {
        return res.status(404).json({
          success: false,
          error: 'Voucher not found',
          code: 'VOUCHER_NOT_FOUND'
        });
      }

      // Create operation object
      const operation: VoucherOperation = {
        type: operationType,
        voucherId: voucher.id,
        newState: req.body.newState,
        metadata: {
          requestBody: req.body,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString()
        }
      };

      // Validate with context-aware system
      const authorization = await validateWithContextAwareSystem(operation, voucher, req.user);

      if (!authorization.authorized) {
        logger.warn(`❌ Context-Aware Veto: Operation ${operationType} BLOCKED`, {
          voucherId: voucher.id,
          violations: authorization.violations,
          blockingReason: authorization.blockingReason
        });

        return res.status(403).json({
          success: false,
          error: 'Operation blocked by context-aware system',
          blockingReason: authorization.blockingReason,
          violations: authorization.violations,
          recommendedAction: authorization.recommendedAction,
          code: 'CONTEXT_AWARE_VETO'
        });
      }

      // Log successful authorization
      logger.info(`✅ Context-Aware Veto: Operation ${operationType} AUTHORIZED`, {
        voucherId: voucher.id,
        userId: req.user?.id,
        department: req.user?.department
      });

      // Store authorization for use in route handler
      req.contextAuthorization = authorization;
      req.voucherOperation = operation;

      next();

    } catch (error) {
      logger.error('Context-Aware Veto middleware error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Context-aware validation failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        code: 'CONTEXT_AWARE_ERROR'
      });
    }
  };
};

/**
 * OPERATION-SPECIFIC MIDDLEWARE FACTORIES
 */
export const vouchersVeto = {
  dispatch: contextAwareVeto('DISPATCH'),
  reject: contextAwareVeto('REJECT'),
  resubmit: contextAwareVeto('RESUBMIT'),
  return: contextAwareVeto('RETURN'),
  certify: contextAwareVeto('CERTIFY'),
  update: contextAwareVeto('UPDATE'),
  delete: contextAwareVeto('DELETE')
};

/**
 * BATCH OPERATION VETO MIDDLEWARE
 */
export const batchContextAwareVeto = (operationType: VoucherOperation['type']) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const voucherIds = req.body.voucherIds || [];
      
      if (!Array.isArray(voucherIds) || voucherIds.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'No voucher IDs provided for batch operation',
          code: 'INVALID_BATCH_REQUEST'
        });
      }

      logger.info(`🛡️ Context-Aware Batch Veto: Validating ${operationType} for ${voucherIds.length} vouchers`);

      const violations: any[] = [];
      const authorizations: AuthorizationResult[] = [];

      // Validate each voucher in the batch
      for (const voucherId of voucherIds) {
        const voucher = await getVoucherFromDatabase(voucherId);
        if (!voucher) {
          violations.push({
            voucherId,
            violationType: 'VOUCHER_NOT_FOUND',
            description: `Voucher ${voucherId} not found`,
            expectedState: 'Existing voucher',
            actualState: 'Not found',
            severity: 'HIGH'
          });
          continue;
        }

        const operation: VoucherOperation = {
          type: operationType,
          voucherId: voucher.id,
          metadata: { batchOperation: true }
        };

        const authorization = await validateWithContextAwareSystem(operation, voucher, req.user);
        authorizations.push(authorization);

        if (!authorization.authorized) {
          violations.push(...authorization.violations);
        }
      }

      if (violations.length > 0) {
        logger.warn(`❌ Context-Aware Batch Veto: Batch ${operationType} BLOCKED`, {
          totalVouchers: voucherIds.length,
          violationCount: violations.length,
          violations
        });

        return res.status(403).json({
          success: false,
          error: 'Batch operation blocked by context-aware system',
          violations,
          totalVouchers: voucherIds.length,
          blockedVouchers: violations.length,
          code: 'CONTEXT_AWARE_BATCH_VETO'
        });
      }

      logger.info(`✅ Context-Aware Batch Veto: Batch ${operationType} AUTHORIZED for ${voucherIds.length} vouchers`);

      req.contextAuthorization = {
        authorized: true,
        violations: [],
        warnings: []
      };

      next();

    } catch (error) {
      logger.error('Context-Aware Batch Veto middleware error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Context-aware batch validation failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        code: 'CONTEXT_AWARE_BATCH_ERROR'
      });
    }
  };
};

/**
 * RESPONSE VALIDATION MIDDLEWARE
 * Ensures API responses comply with context-aware rules
 */
export const contextAwareResponseValidator = (req: Request, res: Response, next: NextFunction) => {
  const originalSend = res.send;
  
  res.send = function(data: any) {
    try {
      // Parse response data if it's a string
      let responseData = data;
      if (typeof data === 'string') {
        try {
          responseData = JSON.parse(data);
        } catch {
          // Not JSON, skip validation
          return originalSend.call(this, data);
        }
      }

      // Validate voucher data in response
      if (responseData && (responseData.vouchers || responseData.voucher)) {
        const vouchers = responseData.vouchers || [responseData.voucher];
        
        logger.info(`🔍 Context-Aware Response Validation: Checking ${vouchers.length} vouchers`);
        
        // This would integrate with the frontend context-aware system
        // For now, we'll log the validation
        vouchers.forEach((voucher: any) => {
          if (voucher && voucher.id) {
            logger.debug(`📋 Voucher ${voucher.id} in response - Type: ${voucher.voucher_type}, Status: ${voucher.status}`);
          }
        });
      }

      return originalSend.call(this, data);
      
    } catch (error) {
      logger.error('Context-Aware Response Validation error:', error);
      return originalSend.call(this, data);
    }
  };
  
  next();
};

/**
 * HELPER FUNCTIONS
 */
async function getVoucherFromDatabase(voucherId: string): Promise<any> {
  try {
    const { query } = await import('../database/db.js');

    const rows = await query(
      'SELECT * FROM vouchers WHERE id = ? AND deleted = 0',
      [voucherId]
    );

    return rows[0] || null;
  } catch (error) {
    logger.error('Error fetching voucher from database:', error);
    return null;
  }
}

async function validateWithContextAwareSystem(
  operation: VoucherOperation,
  voucher: any,
  user: any
): Promise<AuthorizationResult> {
  // This will integrate with the frontend context-aware system
  // For now, implement basic validation logic
  
  try {
    // Basic validation rules
    const violations: AuthorizationResult['violations'] = [];
    
    // Rule 1: Cannot dispatch rejected copies
    if (operation.type === 'DISPATCH' && voucher.voucher_type === 'COPY' && voucher.is_rejected) {
      violations.push({
        voucherId: voucher.id,
        violationType: 'INVALID_TAB_PLACEMENT',
        description: 'Cannot dispatch rejected copy voucher',
        expectedState: 'Permanent record only',
        actualState: 'Attempting dispatch',
        severity: 'CRITICAL'
      });
    }
    
    // Rule 2: Cannot certify rejected vouchers directly
    if (operation.type === 'CERTIFY' && voucher.is_rejected) {
      violations.push({
        voucherId: voucher.id,
        violationType: 'WORKFLOW_BYPASS',
        description: 'Cannot certify rejected voucher directly',
        expectedState: 'Must be resubmitted first',
        actualState: 'Attempting direct certification',
        severity: 'CRITICAL'
      });
    }
    
    // Rule 3: Cannot reject already rejected vouchers
    if (operation.type === 'REJECT' && voucher.is_rejected) {
      violations.push({
        voucherId: voucher.id,
        violationType: 'WORKFLOW_BYPASS',
        description: 'Cannot reject already rejected voucher',
        expectedState: 'Normal or resubmitted voucher',
        actualState: 'Already rejected',
        severity: 'HIGH'
      });
    }
    
    const authorized = violations.length === 0;
    
    return {
      authorized,
      violations,
      warnings: [],
      blockingReason: authorized ? undefined : violations[0]?.description,
      recommendedAction: authorized ? undefined : 'Follow the validated workflow rules'
    };
    
  } catch (error) {
    logger.error('Context-aware validation error:', error);
    
    return {
      authorized: false,
      violations: [{
        voucherId: voucher.id,
        violationType: 'WORKFLOW_BYPASS',
        description: 'Validation system error',
        expectedState: 'Valid operation',
        actualState: 'System error',
        severity: 'CRITICAL'
      }],
      warnings: [],
      blockingReason: 'System error during validation'
    };
  }
}
