import { NewVouchersTab } from "@/components/voucher-hub/new-vouchers-tab";
import { PendingDispatchTab } from "@/components/voucher-hub/pending-dispatch-tab";
import { DispatchedVouchersTab } from "@/components/voucher-hub/dispatched-vouchers-tab";
import { ReturnedVouchersTab } from "@/components/voucher-hub/returned-vouchers-tab";
import { RejectedVouchersTab } from "@/components/voucher-hub/rejected-vouchers-tab";
import { Department, Voucher } from "@/lib/types";
import { DispatchControls } from "./dispatch-controls";
import { useAppStore } from '@/lib/store';
import { ContextAwareVoucherTable } from '@/components/context-aware/ContextAwareVoucherTable';

interface TabContentProps {
  activeTab: string;
  filteredVouchers: Voucher[];
  auditUsers: string[];
  sortColumn: string | null;
  sortDirection: 'asc' | 'desc';
  department: Department;
  voucherEdits: Record<string, any>;
  selectedVouchers: string[];
  onSort: (column: string) => void;
  onVoucherEdit: (voucherId: string, field: string, value: any) => void;
  onSaveVoucherEdits: (voucherId: string) => void;
  onReturnToNew: (voucherId: string) => void;
  onViewVoucher: (voucher: Voucher) => void;
  onDeleteVoucher: (voucherId: string) => void;
  onSelectVoucher: (voucherId: string) => void;
  onSelectAll: () => void;
  setSelectedVouchers: (vouchers: string[]) => void;
  dispatchedBy?: string;
  customDispatchName?: string;
  setDispatchedBy?: (value: string) => void;
  setCustomDispatchName?: (value: string) => void;
  handleSendToDepartment?: () => void;
  isEditable?: boolean;
  setActiveTab?: (tab: string) => void;
}

export function TabContent({
  activeTab,
  filteredVouchers,
  auditUsers,
  sortColumn,
  sortDirection,
  department,
  voucherEdits,
  selectedVouchers,
  onSort,
  onVoucherEdit,
  onSaveVoucherEdits,
  onReturnToNew,
  onViewVoucher,
  onDeleteVoucher,
  onSelectVoucher,
  onSelectAll,
  setSelectedVouchers,
  dispatchedBy,
  customDispatchName,
  setDispatchedBy,
  setCustomDispatchName,
  handleSendToDepartment,
  isEditable = true,
  setActiveTab
}: TabContentProps) {
  const currentUser = useAppStore((state) => state.currentUser);
  const isAuditUser = currentUser?.department === 'AUDIT';

  const renderTabContent = () => {
    switch (activeTab) {
      case 'new-vouchers':
        return (
          <NewVouchersTab
            filteredVouchers={filteredVouchers}
            voucherEdits={voucherEdits}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            handleSort={onSort}
            handleVoucherEdit={onVoucherEdit}
            handleSaveVoucherEdits={onSaveVoucherEdits}
            auditUsers={auditUsers}
            setActiveTab={setActiveTab}
          />
        );
      case 'pending-dispatch':
        return (
          <div className="space-y-4">
            {/* Place dispatch controls above the table when in pending-dispatch tab */}
            {activeTab === 'pending-dispatch' && filteredVouchers.length > 0 && dispatchedBy && setDispatchedBy && (
              <DispatchControls
                pendingDispatchVouchersCount={filteredVouchers.length}
                dispatchedBy={dispatchedBy}
                customDispatchName={customDispatchName || ''}
                selectedVouchers={selectedVouchers}
                setDispatchedBy={setDispatchedBy}
                setCustomDispatchName={setCustomDispatchName || (() => {})}
                handleSendToDepartment={handleSendToDepartment || (() => {})}
                department={department}
              />
            )}
            <PendingDispatchTab
              filteredVouchers={filteredVouchers}
              selectedVouchers={selectedVouchers}
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              handleSort={onSort}
              handleSelectVoucher={onSelectVoucher}
              handleSelectAll={onSelectAll}
              setSelectedVouchers={setSelectedVouchers}
              isAudit={true}
              handleReturnToNew={onReturnToNew}
            />
          </div>
        );
      case 'dispatched':
        return (
          <DispatchedVouchersTab
            filteredVouchers={filteredVouchers}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            handleSort={onSort}
            onViewVoucher={onViewVoucher}
            isEditable={isEditable}
            isAuditUser={isAuditUser}
          />
        );
      case 'returned-vouchers':
        return (
          <ReturnedVouchersTab
            filteredVouchers={filteredVouchers}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            handleSort={onSort}
            handleDeleteReturnedVoucher={onDeleteVoucher}
            isEditable={isEditable}
          />
        );
      case 'rejected-vouchers':
        return (
          <RejectedVouchersTab
            filteredVouchers={filteredVouchers}
            sortColumn={sortColumn}
            sortDirection={sortDirection}
            handleSort={onSort}
            onDeleteVoucher={onDeleteVoucher}
            onViewVoucher={onViewVoucher}
            isAudit={true}
            isEditable={isEditable}
          />
        );
      default:
        return <div>No content available</div>;
    }
  };

  return <div className="mt-4">{renderTabContent()}</div>;
}
