
import { useState, useEffect } from 'react';
import { Department, Voucher } from '@/lib/types';
import { useAppStore } from '@/lib/store';
import { VOUCHER_STATUSES } from '@/lib/constants/voucher-statuses';

export const useVoucherTabs = (department: Department) => {
  const vouchers = useAppStore((state) => state.vouchers);
  const fetchVouchers = useAppStore((state) => state.fetchVouchers);
  const [activeTab, setActiveTab] = useState('new-vouchers');

  // Fetch vouchers on department change - add fetchVouchers to dependencies to prevent stale closure
  useEffect(() => {
    fetchVouchers('ALL'); // Fetch ALL vouchers, then filter by originalDepartment in frontend
  }, [department, fetchVouchers]);

  // Removed problematic useEffect calls that were causing infinite loops

  // SEPARATE WORKFLOW PATHS: NEW VOUCHER tab (Normal + Resubmitted, NO Rejection Copies)
  const newVouchers = vouchers.filter(v => {
    // For audit dashboard viewing department hubs (e.g., FINANCE VOUCHER HUB)
    const check1 = v.originalDepartment === department;
    const check2 = v.department === 'AUDIT';
    const check3 = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING;
    const check4 = v.receivedByAudit === true;
    const check5 = !v.dispatched;
    const check6 = !v.deleted;

    const basicCriteria = check1 && check2 && check3 && check4 && check5 && check6;

    // CLEAN WORKFLOW: Simple voucher type identification
    const isOriginalVoucher = (v as any).voucherType === 'ORIGINAL' || !(v as any).voucherType;
    const isNotRejected = !(v as any).isRejected;
    const isNotWorkStarted = !v.workStarted;

    // NEW VOUCHER tab: Only ORIGINAL vouchers that are NOT rejected and work hasn't started
    return basicCriteria && isOriginalVoucher && isNotRejected && isNotWorkStarted;
  });

  // PENDING DISPATCH TAB: Ready to dispatch vouchers
  const pendingDispatchVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isInAudit = v.department === 'AUDIT';
    const isNotDispatched = !v.dispatched;
    const isNotDeleted = !v.deleted;

    if (!isOriginalDepartment || !isInAudit || isNotDispatched === false || !isNotDeleted) {
      return false;
    }

    // TWO TYPES CAN BE IN PENDING DISPATCH:

    // 1. Normal processed vouchers (work started, ready to dispatch)
    const isProcessedNormalVoucher = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                                    v.receivedByAudit === true &&
                                    v.workStarted === true &&
                                    (!(v as any).is_rejected || (v as any).is_rejected === false) &&
                                    ((v as any).voucher_type === 'ORIGINAL' || !(v as any).voucher_type);

    // 2. CLEAN REJECTION - Original rejected vouchers (ready to dispatch back to department)
    const isRejectedOriginalVoucher = v.status === VOUCHER_STATUSES.AUDIT_PROCESSING &&
                                     (v as any).is_rejected === true &&
                                     (v as any).voucher_type === 'ORIGINAL';

    return isProcessedNormalVoucher || isRejectedOriginalVoucher;
  });
  // DISPATCHED TAB: Only CERTIFIED vouchers (successful workflow)
  const dispatchedVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime;
    const isNotDeleted = !v.deleted;
    const isCertifiedStatus = v.status === VOUCHER_STATUSES.VOUCHER_CERTIFIED;

    // SIMPLE RULE: Only show CERTIFIED vouchers (successful workflow completion)
    return isOriginalDepartment &&
           isDispatchedByAudit &&
           isNotDeleted &&
           isCertifiedStatus;

  });



  // REJECTED TAB: Only COPY vouchers (permanent rejection records)
  const rejectedVouchers = vouchers.filter(v => {
    const isRejectedStatus = v.status === VOUCHER_STATUSES.VOUCHER_REJECTED;
    const isNotDeleted = !v.deleted;

    if (!isRejectedStatus || !isNotDeleted) {
      return false;
    }

    if (department === 'AUDIT') {
      // AUDIT REJECTED TAB: Show COPY vouchers (permanent records in Finance Voucher Hub)
      const isCopyVoucher = (v as any).voucherType === 'COPY';
      const isInAudit = v.department === 'AUDIT';

      return isCopyVoucher && isInAudit;
    } else {
      // DEPARTMENT REJECTED TAB: Show rejected vouchers received by department
      const isOriginalDepartment = v.originalDepartment === department;
      const isInDepartment = v.department === department;
      const isDispatchedByAudit = v.auditDispatchedBy && v.auditDispatchTime;

      // SIMPLE RULE: Original rejected vouchers that were dispatched back to department
      const isOriginalRejectedVoucher = (v as any).voucherType === 'ORIGINAL' && (v as any).isRejected === true;

      return isOriginalDepartment &&
             isInDepartment &&
             isDispatchedByAudit &&
             isOriginalRejectedVoucher;
    }
  });

  // RETURNED TAB: Returned vouchers (simple)
  const returnedVouchers = vouchers.filter(v => {
    const isOriginalDepartment = v.originalDepartment === department;
    const isReturnedStatus = v.status === VOUCHER_STATUSES.VOUCHER_RETURNED;
    const isNotDeleted = !v.deleted;

    return isOriginalDepartment && isReturnedStatus && isNotDeleted;
  });

  // Tab counts for debugging (removed console logging to prevent loops)

  return {
    activeTab,
    setActiveTab,
    newVouchers,
    pendingDispatchVouchers,
    dispatchedVouchers,
    returnedVouchers,
    rejectedVouchers
  };
};
