const mysql = require('mysql2/promise');

async function fixDatabaseSchema() {
  console.log('🔧 Fixing VMS Database Schema...');
  
  const connection = await mysql.createConnection({
    host: 'localhost',
    port: 3306,
    user: 'root',
    password: 'vms@2025@1989',
    database: 'vms_production'
  });

  try {
    console.log('✅ Connected to database');

    // Fix audit_logs table - add missing user_name column
    console.log('📋 Checking audit_logs table...');
    try {
      await connection.execute(`
        ALTER TABLE audit_logs 
        ADD COLUMN user_name VARCHAR(255) DEFAULT NULL 
        AFTER user_id
      `);
      console.log('✅ Added user_name column to audit_logs');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  user_name column already exists in audit_logs');
      } else {
        console.log('❌ Error adding user_name to audit_logs:', error.message);
      }
    }

    // Fix users table - add missing last_selected_year column
    console.log('📋 Checking users table...');
    try {
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN last_selected_year INT DEFAULT 2025
      `);
      console.log('✅ Added last_selected_year column to users');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  last_selected_year column already exists in users');
      } else {
        console.log('❌ Error adding last_selected_year to users:', error.message);
      }
    }

    // Fix vouchers table - add missing is_rejected column
    console.log('📋 Checking vouchers table...');
    try {
      await connection.execute(`
        ALTER TABLE vouchers 
        ADD COLUMN is_rejected BOOLEAN DEFAULT FALSE
      `);
      console.log('✅ Added is_rejected column to vouchers');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  is_rejected column already exists in vouchers');
      } else {
        console.log('❌ Error adding is_rejected to vouchers:', error.message);
      }
    }

    // Fix vouchers table - add missing rejection_type column
    try {
      await connection.execute(`
        ALTER TABLE vouchers 
        ADD COLUMN rejection_type VARCHAR(50) DEFAULT NULL
      `);
      console.log('✅ Added rejection_type column to vouchers');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  rejection_type column already exists in vouchers');
      } else {
        console.log('❌ Error adding rejection_type to vouchers:', error.message);
      }
    }

    // Verify the fixes
    console.log('\n🔍 Verifying schema fixes...');
    
    // Check audit_logs columns
    const [auditCols] = await connection.execute('DESCRIBE audit_logs');
    const hasUserName = auditCols.some(col => col.Field === 'user_name');
    console.log(`audit_logs.user_name: ${hasUserName ? '✅' : '❌'}`);

    // Check users columns
    const [userCols] = await connection.execute('DESCRIBE users');
    const hasLastSelectedYear = userCols.some(col => col.Field === 'last_selected_year');
    console.log(`users.last_selected_year: ${hasLastSelectedYear ? '✅' : '❌'}`);

    // Check vouchers columns
    const [voucherCols] = await connection.execute('DESCRIBE vouchers');
    const hasIsRejected = voucherCols.some(col => col.Field === 'is_rejected');
    const hasRejectionType = voucherCols.some(col => col.Field === 'rejection_type');
    console.log(`vouchers.is_rejected: ${hasIsRejected ? '✅' : '❌'}`);
    console.log(`vouchers.rejection_type: ${hasRejectionType ? '✅' : '❌'}`);

    console.log('\n🎉 Database schema fix completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing database schema:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

fixDatabaseSchema().catch(console.error);
