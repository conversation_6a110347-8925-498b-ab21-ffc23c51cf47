import { v4 as uuidv4 } from "uuid";
import { query, getTransaction } from "../database/db.js";
import { logger } from "../utils/logger.js";
import { simpleEventBus } from "../events/simpleEventBus.js";

/**
 * CLEAN REJECTION WORKFLOW SERVICE
 * ================================
 * 
 * Simple, clean implementation of your exact workflow:
 * 1. Reject voucher → Original goes to PENDING DISPATCH, Co<PERSON> goes to REJECTED
 * 2. Dispatch original → Goes to Finance REJECTED tab
 * 3. Resubmit → Follows normal workflow with badges
 */

export interface CleanRejectionResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export interface RejectionRequest {
  voucherId: string;
  rejectedBy: string;
  rejectionReason: string;
}

export interface ResubmissionRequest {
  voucherId: string;
  resubmittedBy: string;
  resubmissionComment?: string;
}

export class CleanRejectionService {
  
  /**
   * STEP 1: REJECT VOUCHER
   * Creates original (PENDING DISPATCH) and copy (REJECTED) vouchers
   */
  static async rejectVoucher(request: RejectionRequest): Promise<CleanRejectionResult> {
    const connection = await getTransaction();
    
    try {
      await connection.beginTransaction();
      
      const { voucherId, rejectedBy, rejectionReason } = request;
      
      // Get the voucher to reject
      const [vouchers] = await connection.query(
        "SELECT * FROM vouchers WHERE id = ? OR voucher_id = ?", 
        [voucherId, voucherId]
      ) as any[];
      
      if (!vouchers || vouchers.length === 0) {
        throw new Error(`Voucher ${voucherId} not found`);
      }
      
      const voucher = vouchers[0];
      
      // Validate voucher can be rejected
      if (voucher.status === "VOUCHER REJECTED") {
        throw new Error("Voucher is already rejected");
      }
      
      if (voucher.status === "VOUCHER CERTIFIED") {
        throw new Error("Cannot reject a certified voucher");
      }
      
      const rejectionTime = new Date().toISOString().replace("T", " ").substring(0, 19);
      
      // STEP 1A: Update original voucher → PENDING DISPATCH tab
      await connection.query(`
        UPDATE vouchers SET
          status = "AUDIT: PROCESSING",
          voucher_type = "ORIGINAL",
          is_rejected = TRUE,
          rejected_by = ?,
          rejection_time = ?,
          comment = ?,
          dispatched = FALSE,
          received_by_audit = TRUE
        WHERE id = ?
      `, [rejectedBy, rejectionTime, rejectionReason, voucher.id]);

      logger.info(`✅ Original voucher ${voucher.voucher_id} moved to PENDING DISPATCH tab`);
      
      // STEP 1B: Create copy voucher → Finance Voucher Hub REJECTED tab
      const copyId = uuidv4();
      await connection.query(`
        INSERT INTO vouchers (
          id, voucher_id, date, claimant, description, amount, currency,
          department, original_department, status, voucher_type,
          is_rejected, rejected_by, rejection_time, comment,
          original_voucher_id, created_by, created_at, dispatched,
          received_by_audit, sent_to_audit
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        copyId,
        voucher.voucher_id + "-COPY",
        voucher.date,
        voucher.claimant,
        voucher.description,
        voucher.amount,
        voucher.currency,
        "FINANCE", // Copy goes to FINANCE for Finance Voucher Hub REJECTED tab
        voucher.original_department || voucher.department,
        "VOUCHER REJECTED",
        "COPY",
        true,
        rejectedBy,
        rejectionTime,
        rejectionReason,
        voucher.id,
        rejectedBy,
        new Date(),
        false, // Not dispatched (permanent record)
        false, // Not received by audit (it's a copy)
        false  // Not sent to audit (it's a copy)
      ]);

      logger.info(`✅ Copy voucher ${voucher.voucher_id}-COPY created in Finance Voucher Hub REJECTED tab`);
      
      await connection.commit();
      
      return {
        success: true,
        message: "Voucher rejected successfully with clean workflow",
        data: {
          originalId: voucher.id,
          copyId,
          rejectionTime,
          rejectedBy,
          rejectionReason
        }
      };

    } catch (error) {
      await connection.rollback();
      logger.error("Clean rejection failed:", error);
      return {
        success: false,
        message: "Failed to reject voucher",
        error: error instanceof Error ? error.message : "Unknown error"
      };
    } finally {
      connection.release();
    }
  }

  /**
   * CLEAN RESUBMISSION WORKFLOW
   * Resubmit a rejected voucher back to Audit with proper tracking
   */
  static async resubmitVoucher(request: ResubmissionRequest): Promise<CleanRejectionResult> {
    const connection = await getTransaction();

    try {
      await connection.beginTransaction();

      const { voucherId, resubmittedBy, resubmissionComment } = request;

      // Find the rejected voucher
      const [voucherRows] = await connection.query(
        "SELECT * FROM vouchers WHERE id = ? AND is_rejected = TRUE AND voucher_type = 'ORIGINAL'",
        [voucherId]
      ) as any[];

      if (!voucherRows || voucherRows.length === 0) {
        return {
          success: false,
          message: "Rejected voucher not found or not eligible for resubmission"
        };
      }

      const voucher = voucherRows[0];
      const resubmissionTime = new Date().toISOString().replace("T", " ").substring(0, 19);
      const currentResubmissionCount = (voucher.resubmission_count || 0) + 1;

      // STEP 1: Update voucher for resubmission
      await connection.query(`
        UPDATE vouchers SET
          status = "PENDING SUBMISSION",
          is_rejected = FALSE,
          resubmission_count = ?,
          last_resubmitted_by = ?,
          last_resubmission_date = ?,
          comment = ?,
          sent_to_audit = FALSE,
          received_by_audit = FALSE,
          work_started = FALSE,
          dispatched = FALSE,
          audit_dispatched_by = NULL,
          audit_dispatch_time = NULL
        WHERE id = ?
      `, [
        currentResubmissionCount,
        resubmittedBy,
        resubmissionTime,
        resubmissionComment || `Resubmitted on ${resubmissionTime} (Attempt #${currentResubmissionCount})`,
        voucher.id
      ]);

      logger.info(`✅ Voucher ${voucher.voucher_id} resubmitted by ${resubmittedBy} (Attempt #${currentResubmissionCount})`);

      await connection.commit();

      return {
        success: true,
        message: `Voucher resubmitted successfully (Attempt #${currentResubmissionCount})`,
        data: {
          voucherId: voucher.id,
          voucherNumber: voucher.voucher_id,
          resubmittedBy,
          resubmissionTime,
          resubmissionCount: currentResubmissionCount,
          resubmissionComment
        }
      };

    } catch (error) {
      await connection.rollback();
      logger.error("Clean resubmission failed:", error);

      return {
        success: false,
        message: "Failed to resubmit voucher",
        error: error instanceof Error ? error.message : "Unknown error"
      };
    } finally {
      connection.release();
    }
  }
}
