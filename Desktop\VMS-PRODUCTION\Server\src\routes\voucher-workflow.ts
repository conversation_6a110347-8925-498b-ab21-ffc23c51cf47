import express from 'express';
import { authenticate } from '../middleware/auth.js';
import { logger } from '../utils/logger.js';
import { query } from '../database/db.js';
import { VoucherWorkflowService } from '../services/voucher-workflow.service.js';

export const voucherWorkflowRouter = express.Router();

// Apply authentication middleware to all routes
voucherWorkflowRouter.use(authenticate);

/**
 * REJECT VOUCHER - Complete dual-tab rejection workflow
 * POST /api/voucher-workflow/reject
 */
voucherWorkflowRouter.post('/reject', async (req, res) => {
  try {
    const { voucherId, rejectionReason } = req.body;
    
    if (!voucherId || !rejectionReason) {
      return res.status(400).json({
        success: false,
        message: 'Voucher ID and rejection reason are required'
      });
    }
    
    // Validate user has permission to reject vouchers
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Only Audit department can reject vouchers'
      });
    }
    
    // REDIRECT TO CLEAN REJECTION API
    res.status(400).json({
      success: false,
      message: 'This endpoint is deprecated. Use /api/clean-rejection/reject instead.',
      redirectTo: '/api/clean-rejection/reject'
    });
    
  } catch (error) {
    logger.error('Voucher rejection endpoint error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during voucher rejection',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * RESUBMIT VOUCHER - Complete resubmission workflow
 * POST /api/voucher-workflow/resubmit
 */
voucherWorkflowRouter.post('/resubmit', async (req, res) => {
  try {
    const { voucherId, resubmissionComment } = req.body;
    
    if (!voucherId) {
      return res.status(400).json({
        success: false,
        message: 'Voucher ID is required'
      });
    }
    
    // Validate user has permission to resubmit vouchers
    if (req.user.department === 'AUDIT') {
      return res.status(403).json({
        success: false,
        message: 'Audit department cannot resubmit vouchers. Only departments can resubmit their rejected vouchers.'
      });
    }
    
    const result = await VoucherWorkflowService.resubmitVoucher({
      voucherId,
      resubmittedBy: req.user.name,
      resubmissionComment: resubmissionComment || 'Resubmitted for review',
      department: req.user.department
    });
    
    if (result.success) {
      logger.info(`Voucher ${voucherId} resubmitted by ${req.user.name}`, {
        voucherId,
        resubmittedBy: req.user.name,
        resubmissionComment
      });
      
      res.status(200).json(result);
    } else {
      res.status(400).json(result);
    }
    
  } catch (error) {
    logger.error('Voucher resubmission endpoint error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during voucher resubmission',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * CERTIFY RESUBMITTED VOUCHER - Certify a resubmitted voucher
 * POST /api/voucher-workflow/certify-resubmitted/:voucherId
 */
voucherWorkflowRouter.post('/certify-resubmitted/:voucherId', async (req, res) => {
  try {
    const { voucherId } = req.params;
    
    if (!voucherId) {
      return res.status(400).json({
        success: false,
        message: 'Voucher ID is required'
      });
    }
    
    // Validate user has permission to certify vouchers
    if (req.user.department !== 'AUDIT' && req.user.department !== 'SYSTEM ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Only Audit department can certify vouchers'
      });
    }
    
    // Simple certification - just update status to CERTIFIED
    res.status(200).json({
      success: true,
      message: 'Voucher certification feature simplified - use normal workflow',
      data: { voucherId, certifiedBy: req.user.name }
    });
    
  } catch (error) {
    logger.error('Resubmitted voucher certification endpoint error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during resubmitted voucher certification',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET VOUCHER WORKFLOW STATUS - Get complete workflow status and history
 * GET /api/voucher-workflow/status/:voucherId
 */
voucherWorkflowRouter.get('/status/:voucherId', async (req, res) => {
  try {
    const { voucherId } = req.params;
    
    if (!voucherId) {
      return res.status(400).json({
        success: false,
        message: 'Voucher ID is required'
      });
    }
    
    const result = await VoucherWorkflowService.getVoucherWorkflowStatus(voucherId);
    
    if (result.success) {
      res.status(200).json(result);
    } else {
      res.status(404).json(result);
    }
    
  } catch (error) {
    logger.error('Voucher workflow status endpoint error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while getting voucher workflow status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET REJECTED VOUCHERS - Get all rejected vouchers for a department
 * GET /api/voucher-workflow/rejected?department=FINANCE
 */
voucherWorkflowRouter.get('/rejected', async (req, res) => {
  try {
    const { department } = req.query;
    const userDepartment = req.user.department;
    
    // Determine which department's rejected vouchers to fetch
    let targetDepartment = department as string;
    
    if (userDepartment === 'AUDIT' || userDepartment === 'SYSTEM ADMIN') {
      // Audit can see all rejected vouchers or filter by department
      if (!targetDepartment) {
        targetDepartment = 'ALL';
      }
    } else {
      // Other departments can only see their own rejected vouchers
      targetDepartment = userDepartment;
    }
    
    let queryString = `
      SELECT 
        id, voucher_id, claimant, description, amount, currency,
        status, workflow_state, rejection_workflow_stage,
        rejected_by, rejection_time, original_rejection_reason,
        resubmission_count, resubmission_badge, last_resubmitted_by,
        last_resubmission_date, department, original_department
      FROM vouchers 
      WHERE workflow_state IN ('REJECTED', 'RESUBMITTED', 'CERTIFIED_AFTER_RESUBMISSION')
    `;
    
    const queryParams: any[] = [];
    
    if (targetDepartment !== 'ALL') {
      queryString += ` AND (department = ? OR original_department = ?)`;
      queryParams.push(targetDepartment, targetDepartment);
    }
    
    queryString += ` ORDER BY rejection_time DESC`;
    
    const [rejectedVouchers] = await query(queryString, queryParams) as any[];
    
    res.status(200).json({
      success: true,
      message: 'Rejected vouchers retrieved successfully',
      data: {
        vouchers: rejectedVouchers,
        count: rejectedVouchers.length,
        department: targetDepartment
      }
    });
    
  } catch (error) {
    logger.error('Get rejected vouchers endpoint error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while getting rejected vouchers',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET RESUBMITTED VOUCHERS - Get all resubmitted vouchers
 * GET /api/voucher-workflow/resubmitted
 */
voucherWorkflowRouter.get('/resubmitted', async (req, res) => {
  try {
    const userDepartment = req.user.department;
    
    let queryString2 = `
      SELECT
        id, voucher_id, claimant, description, amount, currency,
        status, workflow_state, rejection_workflow_stage,
        resubmission_count, resubmission_badge, last_resubmitted_by,
        last_resubmission_date, original_rejection_reason, original_rejected_by,
        department, original_department
      FROM vouchers
      WHERE workflow_state IN ('RESUBMITTED', 'CERTIFIED_AFTER_RESUBMISSION')
      AND resubmission_count > 0
    `;

    const queryParams: any[] = [];

    if (userDepartment !== 'AUDIT' && userDepartment !== 'SYSTEM ADMIN') {
      queryString2 += ` AND (department = ? OR original_department = ?)`;
      queryParams.push(userDepartment, userDepartment);
    }

    queryString2 += ` ORDER BY last_resubmission_date DESC`;

    const [resubmittedVouchers] = await query(queryString2, queryParams) as any[];
    
    res.status(200).json({
      success: true,
      message: 'Resubmitted vouchers retrieved successfully',
      data: {
        vouchers: resubmittedVouchers,
        count: resubmittedVouchers.length
      }
    });
    
  } catch (error) {
    logger.error('Get resubmitted vouchers endpoint error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while getting resubmitted vouchers',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default voucherWorkflowRouter;
