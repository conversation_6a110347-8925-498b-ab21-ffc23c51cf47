const mysql = require('mysql2/promise');

async function testDatabaseConnection() {
  console.log('Testing database connection...');
  
  const configs = [
    {
      name: 'Production Config (Port 3306)',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'vms@2025@1989',
        database: 'vms_production'
      }
    },
    {
      name: 'Development Config (Port 3307)',
      config: {
        host: 'localhost',
        port: 3307,
        user: 'root',
        password: 'vms@2025@1989',
        database: 'vms_development'
      }
    },
    {
      name: 'Basic Connection (No Database)',
      config: {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'vms@2025@1989'
      }
    }
  ];

  for (const { name, config } of configs) {
    try {
      console.log(`\n--- Testing ${name} ---`);
      console.log(`Host: ${config.host}:${config.port}`);
      console.log(`User: ${config.user}`);
      console.log(`Database: ${config.database || 'none'}`);
      
      const connection = await mysql.createConnection(config);
      console.log('✅ Connection successful!');
      
      // Test query
      const [rows] = await connection.execute('SELECT 1 as test');
      console.log('✅ Query test successful:', rows);
      
      // Show databases
      const [databases] = await connection.execute('SHOW DATABASES');
      console.log('📋 Available databases:');
      databases.forEach(db => console.log(`  - ${db.Database}`));
      
      await connection.end();
      
    } catch (error) {
      console.log('❌ Connection failed:', error.message);
      console.log('   Error code:', error.code);
      console.log('   Error number:', error.errno);
    }
  }
}

testDatabaseConnection().catch(console.error);
