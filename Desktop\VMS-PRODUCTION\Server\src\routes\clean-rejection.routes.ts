import express from "express";
import { CleanRejectionService } from "../services/clean-rejection.service.js";
import { logger } from "../utils/logger.js";

const cleanRejectionRouter = express.Router();

/**
 * CLEAN REJECTION API ROUTES
 * ==========================
 * 
 * Simple, clean API endpoints for your exact workflow
 */

/**
 * POST /api/clean-rejection/reject
 * Reject a voucher with clean workflow
 */
cleanRejectionRouter.post("/reject", async (req, res) => {
  try {
    const { voucherId, rejectionReason } = req.body;
    
    if (!voucherId || !rejectionReason) {
      return res.status(400).json({
        success: false,
        message: "Voucher ID and rejection reason are required"
      });
    }
    
    // Validate user has permission to reject vouchers
    if (req.user.department !== "AUDIT" && req.user.department !== "SYSTEM ADMIN") {
      return res.status(403).json({
        success: false,
        message: "Only Audit department can reject vouchers"
      });
    }
    
    const result = await CleanRejectionService.rejectVoucher({
      voucherId,
      rejectedBy: req.user.name,
      rejectionReason
    });
    
    if (result.success) {
      logger.info(`Clean rejection: ${voucherId} rejected by ${req.user.name}`, {
        voucherId,
        rejectedBy: req.user.name,
        rejectionReason
      });
      
      res.status(200).json(result);
    } else {
      res.status(400).json(result);
    }
    
  } catch (error) {
    logger.error("Clean rejection endpoint error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error during voucher rejection",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

/**
 * POST /api/clean-rejection/resubmit
 * Resubmit a rejected voucher
 */
cleanRejectionRouter.post("/resubmit", async (req, res) => {
  try {
    const { voucherId, resubmissionComment } = req.body;

    if (!voucherId) {
      return res.status(400).json({
        success: false,
        message: "Voucher ID is required"
      });
    }

    // Validate user has permission to resubmit vouchers (not Audit)
    if (req.user.department === "AUDIT") {
      return res.status(403).json({
        success: false,
        message: "Audit department cannot resubmit vouchers"
      });
    }

    const result = await CleanRejectionService.resubmitVoucher({
      voucherId,
      resubmittedBy: req.user.name,
      resubmissionComment
    });

    if (result.success) {
      logger.info(`Clean resubmission: ${voucherId} resubmitted by ${req.user.name}`, {
        voucherId,
        resubmittedBy: req.user.name,
        resubmissionComment
      });
      res.status(200).json(result);
    } else {
      res.status(400).json(result);
    }
    
  } catch (error) {
    logger.error("Clean resubmission endpoint error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error during voucher resubmission",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});

export { cleanRejectionRouter };
