const mysql = require('mysql2/promise');

async function checkDatabases() {
  console.log('🔍 Checking VMS Databases...\n');
  
  const vmsDatabases = ['vms30', 'vms_2026', 'vms_clean_db', 'vms_db', 'vms_production'];

  for (const dbName of vmsDatabases) {
    console.log(`\n📊 === DATABASE: ${dbName} ===`);
    
    try {
      const connection = await mysql.createConnection({
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'vms@2025@1989',
        database: dbName
      });
      
      // Check if key tables exist
      const [tables] = await connection.query('SHOW TABLES');
      const tableNames = tables.map(t => Object.values(t)[0]);
      
      console.log(`📋 Tables (${tableNames.length}):`, tableNames.join(', '));
      
      // Check for key VMS tables
      const keyTables = ['users', 'vouchers', 'batches', 'audit_logs'];
      const hasKeyTables = keyTables.filter(table => tableNames.includes(table));
      console.log(`🔑 Key VMS tables: ${hasKeyTables.join(', ')} (${hasKeyTables.length}/${keyTables.length})`);
      
      if (hasKeyTables.includes('users')) {
        const [userCount] = await connection.query('SELECT COUNT(*) as count FROM users');
        console.log(`👥 Users: ${userCount[0].count}`);
      }
      
      if (hasKeyTables.includes('vouchers')) {
        const [voucherCount] = await connection.query('SELECT COUNT(*) as count FROM vouchers');
        console.log(`📄 Vouchers: ${voucherCount[0].count}`);
      }
      
      if (hasKeyTables.includes('batches')) {
        const [batchCount] = await connection.query('SELECT COUNT(*) as count FROM batches');
        console.log(`📦 Batches: ${batchCount[0].count}`);
      }
      
      if (hasKeyTables.includes('audit_logs')) {
        const [auditCount] = await connection.query('SELECT COUNT(*) as count FROM audit_logs');
        console.log(`📝 Audit logs: ${auditCount[0].count}`);
      }
      
      // Calculate completeness score
      const completeness = hasKeyTables.length;
      console.log(`🎯 Completeness: ${completeness}/4 ${completeness === 4 ? '✅ COMPLETE' : completeness >= 3 ? '⚠️ MOSTLY COMPLETE' : '❌ INCOMPLETE'}`);
      
      await connection.end();
      
    } catch (error) {
      console.log(`❌ Error accessing ${dbName}:`, error.message);
    }
  }
}

checkDatabases().catch(console.error);
